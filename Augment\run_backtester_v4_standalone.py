"""
Standalone Backtester Ver4 Entry Point

This script provides a standalone entry point for running the Ver4 backtester
without Jupyter dependency. It uses the shared API authentication system and
maintains exact Ver4 logic with Ver6 performance optimizations.

Usage:
    python run_backtester_v4_standalone.py --ticker NIFTY --date 20-06-2025 --start 09:15 --end 15:30

Features:
- Command line interface
- Shared API authentication
- Ver4 exact logic preservation
- Ver6 performance optimizations
- Comprehensive logging and reporting
"""

import argparse
import sys
import os
import time
import logging
from datetime import datetime
import json
import traceback

# Add current directory to path for imports
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

# Import our modules
from shared_api_manager import get_api, get_manager
from optimized_backtester_v4_logic import OptimizedBacktesterV4Logic
from enhanced_nadarya_watson_signal import check_vander, check_vander_original
from shared_sideways_signal_helper import check_sideways

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('backtester_v4.log'),
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)

def parse_arguments():
    """Parse command line arguments"""
    parser = argparse.ArgumentParser(
        description='Standalone Backtester Ver4 with Shared API Authentication',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python run_backtester_v4_standalone.py --ticker NIFTY --date 20-06-2025 --start 09:15 --end 15:30
  python run_backtester_v4_standalone.py --ticker BANKNIFTY --date 20-06-2025 --start 09:15 --end 15:30 --target-risk 0.3
  python run_backtester_v4_standalone.py --ticker NIFTY --date 20-06-2025 --start 09:15 --end 15:30 --capital 200000
        """
    )
    
    # Required arguments
    parser.add_argument('--ticker', type=str, required=True,
                       help='Ticker symbol (e.g., NIFTY, BANKNIFTY)')
    parser.add_argument('--date', type=str, required=True,
                       help='Trading date in DD-MM-YYYY format (e.g., 20-06-2025)')
    parser.add_argument('--start', type=str, required=True,
                       help='Start time in HH:MM format (e.g., 09:15)')
    parser.add_argument('--end', type=str, required=True,
                       help='End time in HH:MM format (e.g., 15:30)')
    
    # Optional arguments
    parser.add_argument('--exchange', type=str, default='NSE',
                       help='Exchange (default: NSE)')
    parser.add_argument('--tokenid', type=str, default='',
                       help='Token ID (auto-detected if not provided)')
    parser.add_argument('--target-risk', type=float, default=0.2,
                       help='Target risk percentage (default: 0.2)')
    parser.add_argument('--stop-loss-gap', type=float, default=0.3,
                       help='Stop loss gap percentage (default: 0.3)')
    parser.add_argument('--capital', type=float, default=100000,
                       help='Starting capital (default: 100000)')
    parser.add_argument('--option-cost', type=float, default=0.01,
                       help='Option cost percentage (default: 0.01)')
    parser.add_argument('--interest', type=float, default=0.0,
                       help='Interest on balance (default: 0.0)')
    parser.add_argument('--interval', type=int, default=1,
                       help='Data interval in minutes (default: 1)')
    
    # Output options
    parser.add_argument('--output-dir', type=str, default='backtest_results',
                       help='Output directory for results (default: backtest_results)')
    parser.add_argument('--save-data', action='store_true',
                       help='Save raw data to files')
    parser.add_argument('--verbose', action='store_true',
                       help='Enable verbose logging')
    
    # Signal testing options
    parser.add_argument('--test-signals', action='store_true',
                       help='Test signal detection functions')
    parser.add_argument('--signal-only', action='store_true',
                       help='Only run signal detection, skip backtesting')
    parser.add_argument('--disable-momentum-validation', action='store_true',
                       help='Disable momentum validation (use original logic)')
    parser.add_argument('--disable-realtime-detection', action='store_true',
                       help='Disable real-time detection (use 1-minute lag like original)')
    parser.add_argument('--compare-signals', action='store_true',
                       help='Compare enhanced vs original signal detection')
    
    return parser.parse_args()

def setup_logging(verbose=False):
    """Setup logging configuration"""
    if verbose:
        logging.getLogger().setLevel(logging.DEBUG)
        logger.info("🔧 Verbose logging enabled")

def create_output_directory(output_dir):
    """Create output directory if it doesn't exist"""
    if not os.path.exists(output_dir):
        os.makedirs(output_dir)
        logger.info(f"📁 Created output directory: {output_dir}")

def validate_arguments(args):
    """Validate command line arguments"""
    try:
        # Validate date format
        datetime.strptime(args.date, '%d-%m-%Y')
        
        # Validate time format
        datetime.strptime(args.start, '%H:%M')
        datetime.strptime(args.end, '%H:%M')
        
        # Validate numeric arguments
        if args.target_risk < 0 or args.target_risk > 1:
            raise ValueError("Target risk must be between 0 and 1")
        
        if args.stop_loss_gap < 0 or args.stop_loss_gap > 1:
            raise ValueError("Stop loss gap must be between 0 and 1")
        
        if args.capital <= 0:
            raise ValueError("Capital must be positive")
        
        if args.interval <= 0:
            raise ValueError("Interval must be positive")
        
        logger.info("✅ Arguments validated successfully")
        return True
        
    except ValueError as e:
        logger.error(f"❌ Invalid argument: {str(e)}")
        return False

def test_api_connection():
    """Test API connection and authentication"""
    try:
        logger.info("🔐 Testing API connection...")
        
        # Get API instance
        api = get_api()
        
        # Test API call using get_limits() since get_user_details() doesn't exist
        limits = api.get_limits()
        if limits and limits.get('stat') == 'Ok':
            logger.info("✅ API connection successful")
            logger.info(f"� Account limits available")
            return True
        else:
            logger.error("❌ API connection failed")
            return False
            
    except Exception as e:
        logger.error(f"❌ API connection error: {str(e)}")
        return False

def test_signal_detection(args):
    """Test signal detection functions with enhanced momentum validation"""
    try:
        logger.info("🧪 Testing signal detection functions...")

        # Get token ID for the ticker
        api = get_api()

        if args.exchange == 'NSE':
            search_result = api.searchscrip(exchange='NSE', searchtext=args.ticker + '-EQ')
            if search_result and 'values' in search_result and search_result['values']:
                tokenid = search_result['values'][0]['token']
                logger.info(f"📊 Found token ID: {tokenid} for {args.ticker}")
            else:
                logger.error(f"❌ Could not find token ID for {args.ticker}")
                return False
        else:
            tokenid = args.tokenid

        # Test sideways signal first
        logger.info("🔍 Testing sideways signal detection...")
        is_sideways, sideways_text = check_sideways(
            tokenid=tokenid,
            exchange=args.exchange,
            date_input=args.date,
            starttime_input=args.start,
            endtime_input=args.end
        )
        logger.info(f"📊 Sideways result: {is_sideways} - {sideways_text}")

        # Test Nadarya Watson signal with enhanced options
        use_momentum_validation = not args.disable_momentum_validation
        use_realtime_detection = not args.disable_realtime_detection
        logger.info(f"🔍 Testing Nadarya Watson signal detection (momentum: {use_momentum_validation}, realtime: {use_realtime_detection})...")

        from enhanced_nadarya_watson_signal import check_vander_enhanced

        is_vander, vander_text = check_vander_enhanced(
            tokenid=tokenid,
            exchange=args.exchange,
            date_input=args.date,
            starttime_input=args.start,
            endtime_input=args.end,
            enable_momentum_validation=use_momentum_validation,
            enable_realtime_detection=use_realtime_detection
        )

        logger.info(f"📈 Nadarya Watson result: {is_vander} - {vander_text}")

        # Compare signals if requested
        comparison_results = None
        if args.compare_signals:
            logger.info("🔍 Comparing different signal detection modes...")

            from enhanced_nadarya_watson_signal import check_vander_enhanced

            # Test original (no momentum validation, 1-minute lag)
            is_vander_orig, vander_text_orig = check_vander_enhanced(
                tokenid=tokenid,
                exchange=args.exchange,
                date_input=args.date,
                starttime_input=args.start,
                endtime_input=args.end,
                enable_momentum_validation=False,
                enable_realtime_detection=False
            )

            # Test enhanced (with momentum validation and real-time detection)
            is_vander_enh, vander_text_enh = check_vander_enhanced(
                tokenid=tokenid,
                exchange=args.exchange,
                date_input=args.date,
                starttime_input=args.start,
                endtime_input=args.end,
                enable_momentum_validation=True,
                enable_realtime_detection=True
            )

            # Test real-time only (no momentum validation but real-time)
            is_vander_rt, vander_text_rt = check_vander_enhanced(
                tokenid=tokenid,
                exchange=args.exchange,
                date_input=args.date,
                starttime_input=args.start,
                endtime_input=args.end,
                enable_momentum_validation=False,
                enable_realtime_detection=True
            )

            comparison_results = {
                'original': {
                    'signal': is_vander_orig,
                    'text': vander_text_orig,
                    'description': 'Original (1-min lag, no momentum)'
                },
                'realtime_only': {
                    'signal': is_vander_rt,
                    'text': vander_text_rt,
                    'description': 'Real-time only (no lag, no momentum)'
                },
                'enhanced': {
                    'signal': is_vander_enh,
                    'text': vander_text_enh,
                    'description': 'Enhanced (no lag, with momentum)'
                },
                'lag_fixed': is_vander_orig != is_vander_rt,
                'momentum_effect': is_vander_rt != is_vander_enh
            }

            logger.info(f"📊 Original (lag): {is_vander_orig} - {vander_text_orig}")
            logger.info(f"📊 Real-time: {is_vander_rt} - {vander_text_rt}")
            logger.info(f"📊 Enhanced: {is_vander_enh} - {vander_text_enh}")
            logger.info(f"📊 Lag fixed: {'Yes' if comparison_results['lag_fixed'] else 'No'}")
            logger.info(f"📊 Momentum effect: {'Yes' if comparison_results['momentum_effect'] else 'No'}")

        # Determine final signal based on Ver4 logic (both sideways and nadarya must be true)
        final_signal = is_sideways and is_vander
        signal_strength = "STRONG" if final_signal else "NO SIGNAL"

        logger.info(f"🎯 FINAL SIGNAL: {signal_strength} (Sideways: {is_sideways}, Nadarya: {is_vander})")

        # Create signal test results
        signal_results = {
            'timestamp': datetime.now().isoformat(),
            'ticker': args.ticker,
            'tokenid': tokenid,
            'date': args.date,
            'time_range': f"{args.start} - {args.end}",
            'momentum_validation_enabled': use_momentum_validation,
            'sideways': {
                'signal': is_sideways,
                'text': sideways_text
            },
            'nadarya_watson': {
                'signal': is_vander,
                'text': vander_text
            },
            'final_signal': {
                'signal': final_signal,
                'strength': signal_strength,
                'logic': 'Both sideways and nadarya must be true for signal'
            },
            'comparison': comparison_results
        }

        return signal_results

    except Exception as e:
        logger.error(f"❌ Signal detection test failed: {str(e)}")
        return None

def run_backtester(args):
    """Run the backtester with given arguments"""
    try:
        logger.info("🚀 Starting backtester execution...")
        
        # Initialize backtester
        backtester = OptimizedBacktesterV4Logic(
            ticker=args.ticker,
            exchange=args.exchange,
            start=args.start,
            end=args.end,
            date=args.date,
            tokenid=args.tokenid,
            target_risk=args.target_risk,
            stop_loss_gap=args.stop_loss_gap,
            starting_capital=args.capital,
            option_cost=args.option_cost,
            interest_on_balance=args.interest,
            interval=args.interval
        )
        
        logger.info("✅ Backtester initialized successfully")
        
        # Get performance statistics
        performance_stats = backtester.performance_stats
        logger.info(f"📊 Performance Stats: {performance_stats}")
        
        # Create results summary
        results = {
            'timestamp': datetime.now().isoformat(),
            'parameters': {
                'ticker': args.ticker,
                'exchange': args.exchange,
                'date': args.date,
                'time_range': f"{args.start} - {args.end}",
                'target_risk': args.target_risk,
                'stop_loss_gap': args.stop_loss_gap,
                'starting_capital': args.capital,
                'option_cost': args.option_cost,
                'interest_on_balance': args.interest,
                'interval': args.interval
            },
            'performance_stats': performance_stats,
            'current_price': backtester.current_price,
            'signal_names': backtester.signal_names,
            'data_summary': {
                'records_count': len(backtester.data.get('df', [])) if backtester.data.get('df') is not None else 0,
                'tokenid': backtester.data.get('tokenid', 'Unknown')
            }
        }
        
        return results
        
    except Exception as e:
        logger.error(f"❌ Backtester execution failed: {str(e)}")
        logger.error(traceback.format_exc())
        return None

def save_results(results, output_dir, filename_prefix):
    """Save results to JSON file"""
    try:
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        filename = f"{filename_prefix}_{timestamp}.json"
        filepath = os.path.join(output_dir, filename)
        
        with open(filepath, 'w') as f:
            json.dump(results, f, indent=2, default=str)
        
        logger.info(f"💾 Results saved to: {filepath}")
        return filepath
        
    except Exception as e:
        logger.error(f"❌ Failed to save results: {str(e)}")
        return None

def main():
    """Main execution function"""
    try:
        # Parse arguments
        args = parse_arguments()
        
        # Setup logging
        setup_logging(args.verbose)
        
        # Create output directory
        create_output_directory(args.output_dir)
        
        # Validate arguments
        if not validate_arguments(args):
            sys.exit(1)
        
        # Test API connection
        if not test_api_connection():
            sys.exit(1)
        
        logger.info("🎯 Starting Standalone Backtester Ver4")
        logger.info(f"📊 Ticker: {args.ticker}, Date: {args.date}, Time: {args.start}-{args.end}")
        
        start_time = time.time()
        
        # Test signals if requested
        if args.test_signals:
            signal_results = test_signal_detection(args)
            if signal_results:
                save_results(signal_results, args.output_dir, 'signal_test')
            
            if args.signal_only:
                logger.info("✅ Signal testing completed")
                return
        
        # Run backtester
        results = run_backtester(args)
        
        if results:
            # Save results
            save_results(results, args.output_dir, 'backtester_v4')
            
            # Print summary
            execution_time = time.time() - start_time
            logger.info(f"⏱️ Total execution time: {execution_time:.2f} seconds")
            logger.info(f"💰 Current price: {results.get('current_price', 'N/A')}")
            logger.info(f"📈 Data records: {results['data_summary']['records_count']}")
            logger.info(f"🎯 Cache hits: {results['performance_stats']['cache_hits']}")
            logger.info(f"🔄 Cache misses: {results['performance_stats']['cache_misses']}")
            
            logger.info("✅ Backtester execution completed successfully")
        else:
            logger.error("❌ Backtester execution failed")
            sys.exit(1)
        
    except KeyboardInterrupt:
        logger.info("⚠️ Execution interrupted by user")
        sys.exit(1)
    except Exception as e:
        logger.error(f"❌ Unexpected error: {str(e)}")
        logger.error(traceback.format_exc())
        sys.exit(1)

if __name__ == "__main__":
    main()
