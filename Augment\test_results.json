{"timestamp": "2025-06-24T18:53:46.651833", "tests": {"API Authentication": {"status": "PASSED", "execution_time": 21.312450408935547, "result": {"user": "FA293523", "session_age_minutes": 0.00275885, "api_instance_id": 2605841616368, "manager_instance_id": 2605805540592, "limits_available": true}, "error": null}, "Nadarya Watson Signal": {"status": "PASSED", "execution_time": 1.987499475479126, "result": {"signal": true, "signal_text": "Last 3 mins: Lower band signal present", "tokenid": "11630", "test_date": "23-06-2025"}, "error": null}, "Sideways Signal": {"status": "FAILED", "execution_time": 1.286696195602417, "result": null, "error": "Sideways signal should be boolean"}, "Optimized Backtester V4": {"status": "FAILED", "execution_time": 0.439561128616333, "result": null, "error": "Symbol NIFTY not found"}, "Ver6 Performance Features": {"status": "PASSED", "execution_time": 0.16425085067749023, "result": {"max_workers": 16, "batch_size": 1000, "performance_stats": {"data_load_time": 0, "signal_processing_time": 0, "option_processing_time": 0, "total_api_calls": 0, "cache_hits": 0, "cache_misses": 0, "parallel_tasks": 0}}, "error": null}, "Standalone Execution": {"status": "PASSED", "execution_time": 3.6680822372436523, "result": {"help_command_success": false, "help_output_length": 0, "script_exists": true}, "error": null}, "Cache Performance": {"status": "PASSED", "execution_time": 0.4316110610961914, "result": {"first_call_time": 0.1522822380065918, "second_call_time": 0.13814735412597656, "cache_improvement": true, "speed_improvement_ratio": 1.1023174419086277}, "error": null}}, "summary": {"total_tests": 7, "passed": 5, "failed": 2, "errors": ["Sideways Signal: Sideways signal should be boolean", "Optimized Backtester V4: Symbol NIFTY not found"]}}