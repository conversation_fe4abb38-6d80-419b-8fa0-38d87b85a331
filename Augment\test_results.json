{"timestamp": "2025-06-24T18:26:12.287489", "tests": {"API Authentication": {"status": "FAILED", "execution_time": 23.099746704101562, "result": null, "error": "'NorenApi' object has no attribute 'get_user_details'"}, "Nadarya Watson Signal": {"status": "PASSED", "execution_time": 2.4354660511016846, "result": {"signal": true, "signal_text": "Last 3 mins: Lower band signal present", "tokenid": "11630", "test_date": "23-06-2025"}, "error": null}, "Sideways Signal": {"status": "FAILED", "execution_time": 1.390078067779541, "result": null, "error": "Sideways signal should be boolean"}, "Optimized Backtester V4": {"status": "FAILED", "execution_time": 0.5605361461639404, "result": null, "error": "Symbol NIFTY not found"}, "Ver6 Performance Features": {"status": "PASSED", "execution_time": 0.19600129127502441, "result": {"max_workers": 16, "batch_size": 1000, "performance_stats": {"data_load_time": 0, "signal_processing_time": 0, "option_processing_time": 0, "total_api_calls": 0, "cache_hits": 0, "cache_misses": 0, "parallel_tasks": 0}}, "error": null}, "Standalone Execution": {"status": "PASSED", "execution_time": 4.203778982162476, "result": {"help_command_success": false, "help_output_length": 0, "script_exists": true}, "error": null}, "Cache Performance": {"status": "PASSED", "execution_time": 0.5155668258666992, "result": {"first_call_time": 0.17226195335388184, "second_call_time": 0.15601205825805664, "cache_improvement": true, "speed_improvement_ratio": 1.104157943156846}, "error": null}}, "summary": {"total_tests": 7, "passed": 4, "failed": 3, "errors": ["API Authentication: 'NorenApi' object has no attribute 'get_user_details'", "Sideways Signal: Sideways signal should be boolean", "Optimized Backtester V4: Symbol NIFTY not found"]}}